module.exports = {
  roots: ['<rootDir>/src'],
  collectCoverageFrom: [
    'src/**/*.{js,jsx}',
    '!src/**/*.d.ts',
    '!src/index.js',
    '!src/serviceWorkerRegistration.js',
    '!src/reportWebVitals.js',
    '!src/tests/**',
    '!src/**/*.stories.{js,jsx}',
    '!src/**/*.config.{js,jsx}',
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
    './src/components/': {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75,
    },
    './src/hooks/': {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  coverageReporters: ['text', 'lcov', 'html', 'json-summary'],
  setupFiles: [
    '<rootDir>/src/tests/setupTests.js'
  ],
  setupFilesAfterEnv: ['<rootDir>/src/tests/setupTestsAfterEnv.js'],
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{js,jsx}',
    '<rootDir>/src/**/*.{spec,test}.{js,jsx}',
    '<rootDir>/src/tests/**/*.{spec,test}.{js,jsx}',
  ],
  testPathIgnorePatterns: [
    '<rootDir>/src/tests/e2e/',
    '<rootDir>/src/tests/performance/lighthouse.test.js'
  ],
  testEnvironment: 'jsdom',
  testEnvironmentOptions: {
    url: 'http://localhost:3000'
  },
  transform: {
    '^.+\\.(js|jsx)$': 'babel-jest',
    '^.+\\.css$': '<rootDir>/src/tests/cssTransform.js',
    '^(?!.*\\.(js|jsx|css|json)$)': '<rootDir>/src/tests/fileTransform.js',
  },
  transformIgnorePatterns: [
    '[/\\\\]node_modules[/\\\\](?!(antd|@ant-design|rc-.+|@babel/runtime)).+\\.(js|jsx)$',
    '^.+\\.module\\.(css|sass|scss)$',
  ],
  moduleNameMapper: {
    '^.+\\.module\\.(css|sass|scss)$': 'identity-obj-proxy',
    '^src/(.*)$': '<rootDir>/src/$1',
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  moduleFileExtensions: ['js', 'jsx', 'json'],
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname',
  ],
  resetMocks: true,
  clearMocks: true,
  restoreMocks: true,
  testTimeout: 15000,
  maxWorkers: '50%',
  verbose: true,
};
